import 'package:flutter_test/flutter_test.dart';
import 'package:imtrans/services/remote_ocr_service.dart';
import 'package:imtrans/services/local_ocr_service.dart';

void main() {
  group('RemoteOcrService', () {
    late RemoteOcrService remoteOcrService;

    setUp(() {
      remoteOcrService = RemoteOcrService();
    });

    tearDown(() async {
      await remoteOcrService.dispose();
    });

    group('初始化测试', () {
      test('应该成功初始化服务', () async {
        expect(remoteOcrService.isInitialized, false);
        
        await remoteOcrService.initialize();
        
        expect(remoteOcrService.isInitialized, true);
      });

      test('重复初始化应该不会出错', () async {
        await remoteOcrService.initialize();
        expect(remoteOcrService.isInitialized, true);
        
        // 再次初始化
        await remoteOcrService.initialize();
        expect(remoteOcrService.isInitialized, true);
      });
    });

    group('OCR处理测试', () {
      setUp(() async {
        await remoteOcrService.initialize();
      });

      test('未初始化时处理图片应该抛出异常', () async {
        final uninitializedService = RemoteOcrService();
        
        expect(
          () => uninitializedService.processImagesWithOcr(
            imageUrls: ['https://example.com/image.jpg'],
          ),
          throwsA(isA<Exception>()),
        );
      });

      test('空图片列表应该返回空结果', () async {
        final result = await remoteOcrService.processImagesWithOcr(
          imageUrls: [],
        );
        
        expect(result, isEmpty);
      });

      test('处理单个图片应该返回正确的文本元素', () async {
        // 这个测试需要模拟服务器响应，实际实现中需要使用依赖注入
        // 这里只测试基本的流程逻辑
        final imageUrls = ['https://example.com/test-image.jpg'];
        
        // 由于RemoteOcrService直接调用ServerRequest静态方法，
        // 在实际测试中需要重构代码以支持依赖注入
        // 这里只验证方法调用不会抛出异常
        try {
          final result = await remoteOcrService.processImagesWithOcr(
            imageUrls: imageUrls,
          );
          // 在没有真实服务器的情况下，期望返回空列表
          expect(result, isA<List<OcrTextElement>>());
        } catch (e) {
          // 在测试环境中，网络请求可能失败，这是预期的
          expect(e, isA<Exception>());
        }
      });
    });

    group('OCR结果解析测试', () {
      test('应该正确解析服务器返回的OCR结果', () {
        // 创建一个测试用的RemoteOcrService实例来访问私有方法
        // 注意：这需要将_parseOcrResult方法设为public或创建测试友好的接口
        
        final mockTaskResult = {
          'output': {
            'result_text': [
              {
                'text': 'Hello World',
                'bound': [
                  {'x': 100, 'y': 50},
                  {'x': 200, 'y': 50},
                  {'x': 200, 'y': 80},
                  {'x': 100, 'y': 80},
                ],
              },
              {
                'text': '测试文本',
                'bound': [
                  {'x': 150, 'y': 100},
                  {'x': 250, 'y': 100},
                  {'x': 250, 'y': 130},
                  {'x': 150, 'y': 130},
                ],
              },
            ],
          },
        };

        // 由于_parseOcrResult是私有方法，这里只能测试整体流程
        // 在实际实现中，建议将解析逻辑提取为公共方法或创建测试友好的接口
        expect(mockTaskResult['output'], isNotNull);
        final output = mockTaskResult['output'] as Map<String, dynamic>;
        expect(output['result_text'], isA<List>());
        final resultText = output['result_text'] as List;
        expect(resultText.length, equals(2));
      });

      test('应该处理无效的OCR结果格式', () {
        final invalidResults = [
          {}, // 空对象
          {'output': null}, // output为null
          {'output': {'result_text': null}}, // result_text为null
          {'output': {'result_text': []}}, // 空的result_text
          {
            'output': {
              'result_text': [
                {'text': 'Invalid', 'bound': null}, // bound为null
                {'text': null, 'bound': []}, // text为null
              ],
            },
          },
        ];

        for (final invalidResult in invalidResults) {
          // 验证解析器能够处理无效输入而不崩溃
          expect(invalidResult, isA<Map<String, dynamic>>());
        }
      });
    });

    group('边界框坐标转换测试', () {
      test('应该正确转换服务器坐标格式到标准格式', () {
        // 服务器格式：四个点的坐标
        final serverBound = [
          {'x': 477, 'y': 116},
          {'x': 901, 'y': 116},
          {'x': 901, 'y': 194},
          {'x': 477, 'y': 194},
        ];

        // 期望的标准格式：left, top, right, bottom
        final expectedLeft = 477.0;
        final expectedTop = 116.0;
        final expectedRight = 901.0;
        final expectedBottom = 194.0;

        // 验证坐标提取逻辑
        final xCoords = serverBound.map((point) => (point['x'] as num).toDouble()).toList();
        final yCoords = serverBound.map((point) => (point['y'] as num).toDouble()).toList();

        final actualLeft = xCoords.reduce((a, b) => a < b ? a : b);
        final actualRight = xCoords.reduce((a, b) => a > b ? a : b);
        final actualTop = yCoords.reduce((a, b) => a < b ? a : b);
        final actualBottom = yCoords.reduce((a, b) => a > b ? a : b);

        expect(actualLeft, equals(expectedLeft));
        expect(actualTop, equals(expectedTop));
        expect(actualRight, equals(expectedRight));
        expect(actualBottom, equals(expectedBottom));
      });
    });

    group('错误处理测试', () {
      test('网络错误应该被正确处理', () async {
        await remoteOcrService.initialize();
        
        // 使用无效的图片URL来触发网络错误
        final result = await remoteOcrService.processImagesWithOcr(
          imageUrls: ['invalid-url'],
        );
        
        // 期望返回空列表而不是抛出异常
        expect(result, isA<List<OcrTextElement>>());
      });

      test('服务器错误响应应该被正确处理', () async {
        await remoteOcrService.initialize();
        
        // 在实际测试中，这里需要模拟服务器错误响应
        // 由于当前实现直接调用ServerRequest静态方法，需要重构以支持测试
        expect(remoteOcrService.isInitialized, true);
      });
    });

    group('资源清理测试', () {
      test('dispose应该正确清理资源', () async {
        await remoteOcrService.initialize();
        expect(remoteOcrService.isInitialized, true);
        
        await remoteOcrService.dispose();
        expect(remoteOcrService.isInitialized, false);
      });
    });
  });
}
