import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:imtrans/util/ocr_preferences.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  group('OCR Service Selection Logic Tests', () {
    late OcrPreferences ocrPreferences;

    setUp(() {
      // 清理SharedPreferences
      SharedPreferences.setMockInitialValues({});
      ocrPreferences = OcrPreferences.instance;
    });

    tearDown(() {
      // 重置单例状态
      ocrPreferences.resetForTesting();
    });

    group('OCR偏好设置同步测试', () {
      test('初始化后应该正确读取默认设置', () async {
        await ocrPreferences.initialize();
        
        expect(ocrPreferences.initialized, true);
        expect(ocrPreferences.useServerOcr, false); // 默认使用本地OCR
        expect(ocrPreferences.getOcrTypeDescription(), equals('Local OCR'));
      });

      test('设置变更应该立即生效', () async {
        await ocrPreferences.initialize();
        expect(ocrPreferences.useServerOcr, false);
        
        // 切换到服务器OCR
        await ocrPreferences.setUseServerOcr(true);
        expect(ocrPreferences.useServerOcr, true);
        expect(ocrPreferences.getOcrTypeDescription(), equals('Server OCR'));
        
        // 切换回本地OCR
        await ocrPreferences.setUseServerOcr(false);
        expect(ocrPreferences.useServerOcr, false);
        expect(ocrPreferences.getOcrTypeDescription(), equals('Local OCR'));
      });

      test('设置应该持久化到SharedPreferences', () async {
        await ocrPreferences.initialize();
        
        // 设置为服务器OCR
        await ocrPreferences.setUseServerOcr(true);
        
        // 验证SharedPreferences中的值
        final prefs = await SharedPreferences.getInstance();
        expect(prefs.getBool('use_server_ocr'), true);
        
        // 设置为本地OCR
        await ocrPreferences.setUseServerOcr(false);
        expect(prefs.getBool('use_server_ocr'), false);
      });

      test('重新初始化应该恢复保存的设置', () async {
        // 第一次初始化并设置
        await ocrPreferences.initialize();
        await ocrPreferences.setUseServerOcr(true);
        expect(ocrPreferences.useServerOcr, true);
        
        // 重置状态模拟应用重启
        ocrPreferences.resetForTesting();
        expect(ocrPreferences.initialized, false);
        
        // 重新初始化
        await ocrPreferences.initialize();
        
        // 验证设置被正确恢复
        expect(ocrPreferences.useServerOcr, true);
        expect(ocrPreferences.getOcrTypeDescription(), equals('Server OCR'));
      });
    });

    group('服务选择逻辑模拟测试', () {
      test('应该根据偏好设置选择正确的服务类型', () async {
        await ocrPreferences.initialize();
        
        // 模拟服务选择逻辑
        String getSelectedServiceType() {
          return ocrPreferences.useServerOcr ? 'RemoteOcrService' : 'LocalOcrService';
        }
        
        // 默认应该选择本地OCR
        expect(getSelectedServiceType(), equals('LocalOcrService'));
        
        // 切换到服务器OCR
        await ocrPreferences.setUseServerOcr(true);
        expect(getSelectedServiceType(), equals('RemoteOcrService'));
        
        // 切换回本地OCR
        await ocrPreferences.setUseServerOcr(false);
        expect(getSelectedServiceType(), equals('LocalOcrService'));
      });

      test('服务选择应该实时响应偏好设置变更', () async {
        await ocrPreferences.initialize();
        
        List<String> serviceSelectionHistory = [];
        
        // 模拟监听偏好设置变更
        ocrPreferences.addListener(() {
          final serviceType = ocrPreferences.useServerOcr ? 'RemoteOcrService' : 'LocalOcrService';
          serviceSelectionHistory.add(serviceType);
        });
        
        // 进行多次切换
        await ocrPreferences.setUseServerOcr(true);  // 切换到服务器OCR
        await ocrPreferences.setUseServerOcr(false); // 切换回本地OCR
        await ocrPreferences.setUseServerOcr(true);  // 再次切换到服务器OCR
        
        // 验证服务选择历史
        expect(serviceSelectionHistory, equals([
          'RemoteOcrService',  // 第一次切换
          'LocalOcrService',   // 第二次切换
          'RemoteOcrService',  // 第三次切换
        ]));
      });
    });

    group('错误处理和边界情况测试', () {
      test('未初始化时应该返回默认值', () {
        expect(ocrPreferences.initialized, false);
        expect(ocrPreferences.useServerOcr, false); // 默认值
      });

      test('多次设置相同值不应该触发重复通知', () async {
        await ocrPreferences.initialize();
        
        int notificationCount = 0;
        ocrPreferences.addListener(() {
          notificationCount++;
        });
        
        // 设置为true
        await ocrPreferences.setUseServerOcr(true);
        expect(notificationCount, 1);
        
        // 再次设置为true（相同值）
        await ocrPreferences.setUseServerOcr(true);
        expect(notificationCount, 1); // 不应该增加
        
        // 设置为false
        await ocrPreferences.setUseServerOcr(false);
        expect(notificationCount, 2);
      });

      test('并发设置应该正确处理', () async {
        await ocrPreferences.initialize();
        
        // 并发设置不同的值
        final futures = [
          ocrPreferences.setUseServerOcr(true),
          ocrPreferences.setUseServerOcr(false),
          ocrPreferences.setUseServerOcr(true),
        ];
        
        await Future.wait(futures);
        
        // 最终状态应该是一致的
        expect(ocrPreferences.initialized, true);
        expect(ocrPreferences.useServerOcr, isA<bool>());
        
        // 验证SharedPreferences中的值与内存中的值一致
        final prefs = await SharedPreferences.getInstance();
        expect(prefs.getBool('use_server_ocr'), equals(ocrPreferences.useServerOcr));
      });
    });
  });
}
