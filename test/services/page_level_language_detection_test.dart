import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:imtrans/services/translation_workflow_service.dart';
import 'package:imtrans/services/local_ocr_service.dart';
import 'package:imtrans/util/ocr_preferences.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  group('Page-Level Language Detection Tests', () {
    late TranslationWorkflowService workflowService;

    setUp(() {
      SharedPreferences.setMockInitialValues({});
      workflowService = TranslationWorkflowService();
    });

    tearDown(() async {
      await workflowService.dispose();
      OcrPreferences.instance.resetForTesting();
    });

    group('页面级语言检测逻辑测试', () {
      test('应该正确合并多个文本块进行语言检测', () {
        // 模拟多个日语文本块
        final textElements = [
          const OcrTextElement(
            text: 'こんにちは',
            boundingBox: OcrBoundingBox(left: 10, top: 10, right: 100, bottom: 30),
            confidence: 0.9,
          ),
          const OcrTextElement(
            text: '世界',
            boundingBox: OcrBoundingBox(left: 10, top: 40, right: 80, bottom: 60),
            confidence: 0.8,
          ),
          const OcrTextElement(
            text: 'ありがとう',
            boundingBox: OcrBoundingBox(left: 10, top: 70, right: 120, bottom: 90),
            confidence: 0.95,
          ),
        ];

        // 验证文本合并逻辑
        final combinedText = textElements
            .map((element) => element.text.trim())
            .where((text) => text.isNotEmpty)
            .join(' ');

        expect(combinedText, equals('こんにちは 世界 ありがとう'));
        expect(combinedText.length, greaterThan(0));
        
        // 验证有足够的文本内容进行准确的语言检测
        expect(combinedText.length, greaterThan(10)); // 更多文本内容提高检测准确性
      });

      test('应该处理空文本和无效文本块', () {
        final textElements = [
          const OcrTextElement(
            text: '',
            boundingBox: OcrBoundingBox(left: 10, top: 10, right: 100, bottom: 30),
            confidence: 0.9,
          ),
          const OcrTextElement(
            text: '   ',
            boundingBox: OcrBoundingBox(left: 10, top: 40, right: 80, bottom: 60),
            confidence: 0.8,
          ),
          const OcrTextElement(
            text: 'Hello World',
            boundingBox: OcrBoundingBox(left: 10, top: 70, right: 120, bottom: 90),
            confidence: 0.95,
          ),
        ];

        // 验证过滤逻辑
        final validTexts = textElements
            .map((element) => element.text.trim())
            .where((text) => text.isNotEmpty)
            .toList();

        expect(validTexts, equals(['Hello World']));
        expect(validTexts.length, equals(1));
      });

      test('应该处理混合语言内容', () {
        final textElements = [
          const OcrTextElement(
            text: 'Hello',
            boundingBox: OcrBoundingBox(left: 10, top: 10, right: 100, bottom: 30),
            confidence: 0.9,
          ),
          const OcrTextElement(
            text: 'こんにちは',
            boundingBox: OcrBoundingBox(left: 10, top: 40, right: 80, bottom: 60),
            confidence: 0.8,
          ),
          const OcrTextElement(
            text: 'World',
            boundingBox: OcrBoundingBox(left: 10, top: 70, right: 120, bottom: 90),
            confidence: 0.95,
          ),
        ];

        final combinedText = textElements
            .map((element) => element.text.trim())
            .where((text) => text.isNotEmpty)
            .join(' ');

        expect(combinedText, equals('Hello こんにちは World'));
        
        // 在混合语言情况下，语言检测应该能够识别主要语言
        // 这里英语内容更多，应该被识别为英语
        final englishWords = combinedText.split(' ').where((word) => 
          RegExp(r'^[a-zA-Z]+$').hasMatch(word)).length;
        final japaneseWords = combinedText.split(' ').where((word) => 
          RegExp(r'[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]').hasMatch(word)).length;
        
        expect(englishWords, equals(2)); // Hello, World
        expect(japaneseWords, equals(1)); // こんにちは
      });
    });

    group('语言检测优势验证', () {
      test('页面级检测应该比单块检测提供更多上下文', () {
        // 模拟短文本块（单独检测可能不准确）
        final shortTextElements = [
          const OcrTextElement(
            text: 'の',
            boundingBox: OcrBoundingBox(left: 10, top: 10, right: 30, bottom: 30),
            confidence: 0.9,
          ),
          const OcrTextElement(
            text: 'は',
            boundingBox: OcrBoundingBox(left: 40, top: 10, right: 60, bottom: 30),
            confidence: 0.8,
          ),
          const OcrTextElement(
            text: 'を',
            boundingBox: OcrBoundingBox(left: 70, top: 10, right: 90, bottom: 30),
            confidence: 0.95,
          ),
        ];

        // 单个字符很难准确检测语言
        for (final element in shortTextElements) {
          expect(element.text.length, lessThanOrEqualTo(1));
        }

        // 但是合并后的文本提供了更好的上下文
        final combinedText = shortTextElements
            .map((element) => element.text.trim())
            .where((text) => text.isNotEmpty)
            .join(' ');

        expect(combinedText, equals('の は を'));
        expect(combinedText.length, greaterThan(3)); // 更多上下文
        
        // 合并的文本包含多个日语助词，更容易被正确识别为日语
        final hasJapaneseChars = RegExp(r'[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]')
            .hasMatch(combinedText);
        expect(hasJapaneseChars, true);
      });

      test('应该能够处理长文本内容', () {
        // 模拟包含长文本的页面
        final longTextElements = [
          const OcrTextElement(
            text: 'This is a comprehensive test of the page-level language detection system.',
            boundingBox: OcrBoundingBox(left: 10, top: 10, right: 400, bottom: 30),
            confidence: 0.9,
          ),
          const OcrTextElement(
            text: 'It should be able to accurately detect English as the primary language.',
            boundingBox: OcrBoundingBox(left: 10, top: 40, right: 400, bottom: 60),
            confidence: 0.8,
          ),
          const OcrTextElement(
            text: 'Even when there are multiple text blocks on the same page.',
            boundingBox: OcrBoundingBox(left: 10, top: 70, right: 400, bottom: 90),
            confidence: 0.95,
          ),
        ];

        final combinedText = longTextElements
            .map((element) => element.text.trim())
            .where((text) => text.isNotEmpty)
            .join(' ');

        expect(combinedText.length, greaterThan(100));
        expect(combinedText.split(' ').length, greaterThan(20)); // 足够的词汇量
        
        // 长文本应该提供非常准确的语言检测
        final wordCount = combinedText.split(' ').length;
        expect(wordCount, greaterThan(20)); // 充足的上下文
      });
    });

    group('边界情况处理', () {
      test('应该处理空的文本元素列表', () {
        final emptyElements = <OcrTextElement>[];
        
        expect(emptyElements.isEmpty, true);
        
        // 空列表应该返回空的合并文本
        final combinedText = emptyElements
            .map((element) => element.text.trim())
            .where((text) => text.isNotEmpty)
            .join(' ');
        
        expect(combinedText, equals(''));
      });

      test('应该处理只包含空白字符的文本', () {
        final whitespaceElements = [
          const OcrTextElement(
            text: '   ',
            boundingBox: OcrBoundingBox(left: 10, top: 10, right: 100, bottom: 30),
            confidence: 0.9,
          ),
          const OcrTextElement(
            text: '\t\n',
            boundingBox: OcrBoundingBox(left: 10, top: 40, right: 80, bottom: 60),
            confidence: 0.8,
          ),
        ];

        final combinedText = whitespaceElements
            .map((element) => element.text.trim())
            .where((text) => text.isNotEmpty)
            .join(' ');

        expect(combinedText, equals(''));
      });

      test('应该处理特殊字符和符号', () {
        final specialCharElements = [
          const OcrTextElement(
            text: '!@#\$%',
            boundingBox: OcrBoundingBox(left: 10, top: 10, right: 100, bottom: 30),
            confidence: 0.9,
          ),
          const OcrTextElement(
            text: '123456',
            boundingBox: OcrBoundingBox(left: 10, top: 40, right: 80, bottom: 60),
            confidence: 0.8,
          ),
          const OcrTextElement(
            text: 'Hello',
            boundingBox: OcrBoundingBox(left: 10, top: 70, right: 120, bottom: 90),
            confidence: 0.95,
          ),
        ];

        final combinedText = specialCharElements
            .map((element) => element.text.trim())
            .where((text) => text.isNotEmpty)
            .join(' ');

        expect(combinedText, equals('!@#\$% 123456 Hello'));
        
        // 即使包含特殊字符，也应该能够从有效的文本中检测语言
        final hasValidText = combinedText.contains('Hello');
        expect(hasValidText, true);
      });
    });
  });
}
