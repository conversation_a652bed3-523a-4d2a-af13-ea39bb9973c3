import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:imtrans/services/translation_workflow_service.dart';
import 'package:imtrans/util/ocr_preferences.dart';

void main() {
  group('TranslationWorkflowService OCR Integration Tests', () {
    late TranslationWorkflowService workflowService;
    late OcrPreferences ocrPreferences;

    setUp(() {
      // 清理SharedPreferences
      SharedPreferences.setMockInitialValues({});
      workflowService = TranslationWorkflowService();
      ocrPreferences = OcrPreferences.instance;
    });

    tearDown(() async {
      await workflowService.dispose();
      // 重置OCR偏好设置状态
      ocrPreferences._initialized = false;
      ocrPreferences._useServerOcr = false;
    });

    group('服务初始化测试', () {
      test('应该成功初始化所有服务包括OCR偏好设置', () async {
        expect(workflowService.isInitialized, false);
        expect(ocrPreferences.initialized, false);
        
        await workflowService.initialize();
        
        expect(workflowService.isInitialized, true);
        expect(ocrPreferences.initialized, true);
      });

      test('初始化后应该使用默认的本地OCR设置', () async {
        await workflowService.initialize();
        
        expect(ocrPreferences.useServerOcr, false);
        expect(ocrPreferences.getOcrTypeDescription(), equals('Local OCR'));
      });

      test('应该能够从保存的偏好设置中恢复OCR配置', () async {
        // 预设保存的偏好设置为服务器OCR
        SharedPreferences.setMockInitialValues({
          'use_server_ocr': true,
        });
        
        await workflowService.initialize();
        
        expect(ocrPreferences.useServerOcr, true);
        expect(ocrPreferences.getOcrTypeDescription(), equals('Server OCR'));
      });
    });

    group('OCR服务路由测试', () {
      setUp(() async {
        await workflowService.initialize();
      });

      test('默认情况下应该使用本地OCR服务', () async {
        expect(ocrPreferences.useServerOcr, false);
        
        // 验证工作流服务能够正确获取OCR偏好设置
        // 注意：由于_getOcrService是私有方法，这里只能间接测试
        expect(workflowService.isInitialized, true);
      });

      test('切换到服务器OCR后工作流应该使用远程服务', () async {
        // 切换到服务器OCR
        await ocrPreferences.setUseServerOcr(true);
        
        expect(ocrPreferences.useServerOcr, true);
        expect(ocrPreferences.getOcrTypeDescription(), equals('Server OCR'));
        
        // 验证设置已保存
        final prefs = await SharedPreferences.getInstance();
        expect(prefs.getBool('use_server_ocr'), true);
      });

      test('切换回本地OCR后工作流应该使用本地服务', () async {
        // 先切换到服务器OCR
        await ocrPreferences.setUseServerOcr(true);
        expect(ocrPreferences.useServerOcr, true);
        
        // 再切换回本地OCR
        await ocrPreferences.setUseServerOcr(false);
        
        expect(ocrPreferences.useServerOcr, false);
        expect(ocrPreferences.getOcrTypeDescription(), equals('Local OCR'));
        
        // 验证设置已保存
        final prefs = await SharedPreferences.getInstance();
        expect(prefs.getBool('use_server_ocr'), false);
      });
    });

    group('OCR偏好设置变更监听测试', () {
      setUp(() async {
        await workflowService.initialize();
      });

      test('OCR偏好设置变更应该触发监听器', () async {
        bool changeNotified = false;
        
        ocrPreferences.addListener(() {
          changeNotified = true;
        });
        
        await ocrPreferences.setUseServerOcr(true);
        
        expect(changeNotified, true);
        expect(ocrPreferences.useServerOcr, true);
      });

      test('多次设置相同值不应该重复触发监听器', () async {
        int notificationCount = 0;
        
        ocrPreferences.addListener(() {
          notificationCount++;
        });
        
        // 设置为true
        await ocrPreferences.setUseServerOcr(true);
        expect(notificationCount, 1);
        
        // 再次设置为true（相同值）
        await ocrPreferences.setUseServerOcr(true);
        expect(notificationCount, 1); // 不应该增加
        
        // 设置为false
        await ocrPreferences.setUseServerOcr(false);
        expect(notificationCount, 2);
      });
    });

    group('服务兼容性测试', () {
      setUp(() async {
        await workflowService.initialize();
      });

      test('本地OCR和远程OCR应该返回兼容的数据格式', () async {
        // 这个测试验证两种OCR服务返回的数据格式是否兼容
        // 由于实际的OCR处理需要真实的图片和网络连接，
        // 这里主要测试服务的初始化和配置
        
        // 测试本地OCR配置
        await ocrPreferences.setUseServerOcr(false);
        expect(ocrPreferences.useServerOcr, false);
        
        // 测试远程OCR配置
        await ocrPreferences.setUseServerOcr(true);
        expect(ocrPreferences.useServerOcr, true);
        
        // 两种配置都应该能正常工作
        expect(workflowService.isInitialized, true);
      });

      test('OCR服务切换不应该影响其他服务的状态', () async {
        expect(workflowService.isInitialized, true);
        
        // 切换OCR设置
        await ocrPreferences.setUseServerOcr(true);
        expect(workflowService.isInitialized, true);
        
        await ocrPreferences.setUseServerOcr(false);
        expect(workflowService.isInitialized, true);
      });
    });

    group('错误处理和恢复测试', () {
      setUp(() async {
        await workflowService.initialize();
      });

      test('OCR偏好设置保存失败不应该影响服务运行', () async {
        // 在实际测试中，可能需要模拟SharedPreferences保存失败
        // 这里验证基本的错误处理逻辑
        
        expect(workflowService.isInitialized, true);
        
        // 尝试设置OCR偏好
        await ocrPreferences.setUseServerOcr(true);
        
        // 服务应该仍然可用
        expect(workflowService.isInitialized, true);
        expect(ocrPreferences.useServerOcr, true);
      });
    });

    group('资源清理测试', () {
      test('dispose应该正确清理所有OCR相关资源', () async {
        await workflowService.initialize();
        expect(workflowService.isInitialized, true);
        
        await workflowService.dispose();
        expect(workflowService.isInitialized, false);
        
        // OCR偏好设置应该保持其状态（不被清理）
        expect(ocrPreferences.initialized, true);
      });
    });

    group('并发访问测试', () {
      test('并发修改OCR偏好设置应该正确处理', () async {
        await workflowService.initialize();
        
        // 并发设置OCR偏好
        final futures = [
          ocrPreferences.setUseServerOcr(true),
          ocrPreferences.setUseServerOcr(false),
          ocrPreferences.setUseServerOcr(true),
        ];
        
        await Future.wait(futures);
        
        // 最终状态应该是一致的
        expect(ocrPreferences.initialized, true);
        expect(ocrPreferences.useServerOcr, isA<bool>());
      });
    });
  });
}
